<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\DeliveryLocation;
use App\Models\KitchenMaster;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\Setting;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * Product Menu Controller
 *
 * Handles product menu retrieval based on kitchen's active menu type and product categories.
 * Logic: Get kitchen's menu type -> Find active product categories for that type -> Get active products
 */
class ProductMenuController extends Controller
{
    /**
     * Get product menu for a given city and/or delivery location
     *
     * Logic:
     * 1. Get kitchen and its menu type (using existing menu management logic)
     * 2. Find active product categories of type "meal"
     * 3. Get active products that match both the kitchen's menu type and product categories
     * 4. Bifurcate products by category name
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getProductMenu(Request $request): JsonResponse
    {
        try {
            // Validate input parameters
            $validator = Validator::make($request->all(), [
                'city' => 'nullable|string|max:100',
                'delivery_location' => 'nullable|string|max:255',
                'delivery_location_id' => 'nullable|integer|exists:delivery_locations,pk_location_code',
            ]);

            if ($validator->fails()) {
                return $this->validationErrorResponse($validator->errors());
            }

            $city = $request->input('city');
            $deliveryLocation = $request->input('delivery_location');
            $deliveryLocationId = $request->input('delivery_location_id');

            // Step 1: Get kitchen and menu type using existing logic
            $menuData = $this->getKitchenMenuType($city, $deliveryLocation, $deliveryLocationId);

            if (!$menuData['success']) {
                return response()->json($menuData, $menuData['status_code']);
            }

            $kitchen = $menuData['kitchen'];
            $menuType = $menuData['menu_type']; // e.g., "breakfast,lunch"
            $deliveryLocationData = $menuData['delivery_location'];

            // Step 2: Get active product categories of type "meal"
            $activeCategories = ProductCategory::active()
                ->mealType()
                ->orderBy('sequence')
                ->get();

            // Step 3 & 4: Get products and bifurcate by meal category types
            $productsByMealType = $this->getProductsByCategory($activeCategories, $menuType);

            return response()->json([
                'success' => true,
                'message' => 'Product menu retrieved successfully',
                'data' => [
                    'delivery_location' => [
                        'id' => $deliveryLocationData->pk_location_code,
                        'name' => $deliveryLocationData->location,
                        'city' => $deliveryLocationData->city,
                        'fk_kitchen_code' => $deliveryLocationData->fk_kitchen_code,
                    ],
                    'kitchen' => [
                        'code' => $kitchen->pk_kitchen_code,
                        'name' => $kitchen->kitchen_name,
                        'alias' => $kitchen->kitchen_alias,
                    ],
                    'menu_type' => $menuType,
                    'meal_types' => $productsByMealType,
                    'summary' => [
                        'total_meal_types' => count($productsByMealType),
                        'total_products' => array_sum(array_column($productsByMealType, 'product_count')),
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            return $this->exceptionResponse($e, 'Error retrieving product menu', $request->all());
        }
    }

    /**
     * Get kitchen and menu type using existing menu management logic
     *
     * @param string|null $city
     * @param string|null $deliveryLocation
     * @param int|null $deliveryLocationId
     * @return array
     */
    private function getKitchenMenuType(?string $city, ?string $deliveryLocation, ?int $deliveryLocationId): array
    {
        // Step 1: Determine the active delivery location
        $activeDeliveryLocation = $this->getActiveDeliveryLocation($city, $deliveryLocation, $deliveryLocationId);

        if (!$activeDeliveryLocation) {
            return $this->buildErrorResponse(
                'No active delivery location found for the given criteria',
                404,
                [
                    'city' => $city,
                    'delivery_location' => $deliveryLocation,
                    'delivery_location_id' => $deliveryLocationId,
                ]
            );
        }

        // Step 2 & 3: Get kitchen data
        $kitchenData = $this->getKitchenData($activeDeliveryLocation);
        if (!$kitchenData['success']) {
            return $kitchenData;
        }

        $kitchen = $kitchenData['kitchen'];

        // Step 4: Look up menu type in settings (with fallback logic)
        $kitchenMenuTypeKey = strtoupper($kitchen->kitchen_alias) . '_MENU_TYPE';
        $kitchenMenuTypeSetting = Setting::byKey($kitchenMenuTypeKey)->first();

        // Always check for global MENU_TYPE setting
        $globalMenuTypeSetting = Setting::byKey('MENU_TYPE')->first();

        $menuType = null;

        if ($kitchenMenuTypeSetting) {
            // Kitchen-specific setting takes priority
            $menuType = $kitchenMenuTypeSetting->value;
        } elseif ($globalMenuTypeSetting) {
            // Fall back to global MENU_TYPE
            $menuType = $globalMenuTypeSetting->value;
        }

        if (!$menuType) {
            return $this->buildErrorResponse(
                'No menu type configured for this kitchen or globally',
                404,
                [
                    'kitchen_alias' => $kitchen->kitchen_alias,
                    'kitchen_setting_key' => $kitchenMenuTypeKey,
                ]
            );
        }

        return [
            'success' => true,
            'delivery_location' => $activeDeliveryLocation,
            'kitchen' => $kitchen,
            'menu_type' => $menuType,
        ];
    }

    /**
     * Get products bifurcated by meal category types (breakfast, lunch, dinner)
     *
     * @param \Illuminate\Database\Eloquent\Collection $categories
     * @param string $menuType
     * @return array
     */
    private function getProductsByCategory($categories, string $menuType): array
    {
        $result = [];
        $menuTypes = array_map('trim', explode(',', $menuType));
        
        // Get all active products from active meal-type categories
        $allProducts = collect();
        foreach ($categories as $category) {
            $products = Product::active()
                ->withProductCategory($category->product_category_name)
                ->withCategory($menuTypes)
                ->orderBy('pk_product_code', 'asc')
                ->get();
            $allProducts = $allProducts->merge($products);
        }

        // Bifurcate products by meal category types (breakfast, lunch, dinner)
        foreach ($menuTypes as $mealType) {
            $mealTypeProducts = $allProducts->filter(function ($product) use ($mealType) {
                // Check if product's category contains this meal type
                $productCategories = array_map('trim', explode(',', $product->category));
                return in_array($mealType, $productCategories);
            })->sortBy(function ($product) {
                // Sort products: Recommended items first, then by pk_product_code
                $isRecommended = stripos($product->name, 'Recommended') !== false ? 0 : 1;
                return [$isRecommended, $product->pk_product_code];
            })->map(function ($product) use ($mealType) {
                return [
                    'id' => $product->pk_product_code,
                    'name' => $product->name,
                    'description' => $product->description,
                    'unit_price' => $product->unit_price,
                    'category' => $product->category,
                    'product_type' => $product->product_type,
                    'food_type' => $product->food_type,
                    'image_path' => $this->assetUrl($product->image_path, $product->company_id),
                    'is_custom' => $product->is_custom,
                    'sequence' => $product->sequence,
                    'product_category_name' => $product->product_category,
                    'menu_items' => $this->formatMenuItems($product->items, $mealType),
                ];
            })->values();

            if ($mealTypeProducts->isNotEmpty()) {
                $result[] = [
                    'meal_type' => $mealType,
                    'meal_type_display' => ucfirst($mealType),
                    'product_count' => $mealTypeProducts->count(),
                    'products' => $mealTypeProducts->toArray(),
                ];
            }
        }

        return $result;
    }

    private function assetUrl($imagePath, $companyId){
        if(empty($imagePath) || empty($companyId)){
            return null;
        }
        
        $region = config('filesystems.disks.s3.region');
        $bucket = config('filesystems.disks.s3.bucket');

        return 'https://s3.'.$region.'.amazonaws.com/'.$bucket.'/'.$companyId.'/product/'.$imagePath;
    }

    /**
     * Format menu items from JSON string to readable format
     * Uses product_planner table first, then fallbacks to products table
     * Similar to the logic used in quickserve service for meal_items
     *
     * @param string|null $items
     * @param string $menuType
     * @return string
     */
    private function formatMenuItems(?string $items, string $menuType = ''): string
    {
        if (empty($items)) {
            return '';
        }

        try {
            // Try to decode as JSON first (if items are stored as JSON)
            $decodedItems = json_decode($items, true);

            if (json_last_error() === JSON_ERROR_NONE && is_array($decodedItems)) {
                // Extract product IDs from the JSON object
                $productIds = array_keys($decodedItems);

                if (!empty($productIds)) {
                    $itemNames = [];
                    $useDate = now()->format('Y-m-d');

                    foreach ($productIds as $productId) {
                        $itemName = $this->getItemNameFromPlanner($productId, $menuType, $useDate);
                        if ($itemName) {
                            $itemNames[] = $itemName;
                        }
                    }

                    return implode(', ', $itemNames);
                }
            }
        } catch (\Exception $e) {
            // If JSON decode fails or database query fails, treat as plain text
        }

        // If not JSON or JSON decode failed, treat as comma-separated string
        // Clean up the string and return as is
        return trim($items);
    }

    /**
     * Get item name from product_planner table first, fallback to products table
     * Follows the same logic as quickserve service
     *
     * @param int $itemCode
     * @param string $menuType
     * @param string $date
     * @return string|null
     */
    private function getItemNameFromPlanner(int $itemCode, string $menuType, string $date): ?string
    {
        try {
            // First check product_planner table for specific product name
            $plannerItem = DB::table('product_planner')
                ->where('date', $date)
                ->where('menu', strtolower($menuType))
                ->where('fk_kitchen_code', 1) // Default kitchen
                ->where('generic_product_code', $itemCode)
                ->first(['specific_product_code', 'specific_product_name']);

            if ($plannerItem && !empty($plannerItem->specific_product_name)) {
                return $plannerItem->specific_product_name;
            }

            // Fallback to products table
            $product = Product::where('pk_product_code', $itemCode)
                ->first(['name']);

            if ($product) {
                return $product->name;
            }

            return 'Unknown Item';

        } catch (\Exception $e) {
            // If database query fails, fallback to products table
            $product = Product::where('pk_product_code', $itemCode)
                ->first(['name']);

            return $product ? $product->name : 'Unknown Item';
        }
    }

    /**
     * Get active delivery location based on city and/or delivery location parameters
     * (Reused from MenuController)
     */
    private function getActiveDeliveryLocation(?string $city, ?string $deliveryLocation, ?int $deliveryLocationId): ?\App\Models\DeliveryLocation
    {
        // Priority 1: Use delivery_location_id if provided
        if ($deliveryLocationId) {
            return DeliveryLocation::where('pk_location_code', $deliveryLocationId)
                                 ->where('status', 1)
                                 ->first();
        }

        $query = DeliveryLocation::where('status', 1);

        // Priority 2: Use delivery_location name with optional city filter
        if ($deliveryLocation) {
            $query->where('location', 'LIKE', '%' . $deliveryLocation . '%');

            if ($city) {
                $query->where('city', 'LIKE', '%' . $city . '%');
            }

            return $query->first();
        }

        // Priority 3: Use city only
        if ($city) {
            return $query->where('city', 'LIKE', '%' . $city . '%')->first();
        }

        // No criteria provided
        return null;
    }

    /**
     * Helper method to get kitchen data from delivery location
     * (Reused from MenuController)
     */
    private function getKitchenData(DeliveryLocation $deliveryLocation): array
    {
        $kitchenCode = $deliveryLocation->fk_kitchen_code;

        if (!$kitchenCode) {
            return $this->buildErrorResponse(
                'No kitchen code found for the delivery location',
                404,
                [
                    'delivery_location_id' => $deliveryLocation->pk_location_code,
                    'delivery_location_name' => $deliveryLocation->location,
                ]
            );
        }

        $kitchen = KitchenMaster::where('pk_kitchen_code', $kitchenCode)->first();

        if (!$kitchen || !$kitchen->kitchen_alias) {
            return $this->buildErrorResponse(
                'No kitchen alias found for kitchen code',
                404,
                [
                    'kitchen_code' => $kitchenCode,
                    'delivery_location_id' => $deliveryLocation->pk_location_code,
                ]
            );
        }

        return [
            'success' => true,
            'kitchen' => $kitchen,
        ];
    }

    /**
     * Helper method to build error responses
     */
    private function buildErrorResponse(string $message, int $statusCode, array $data = []): array
    {
        return [
            'success' => false,
            'message' => $message,
            'status_code' => $statusCode,
            'data' => $data,
        ];
    }

    /**
     * Helper method for validation error responses
     */
    private function validationErrorResponse($errors): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => 'Validation failed',
            'errors' => $errors,
        ], 422);
    }

    /**
     * Helper method for exception responses
     */
    private function exceptionResponse(\Exception $e, string $message, array $requestData = []): JsonResponse
    {
        Log::error($message, [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
            'request_data' => $requestData,
        ]);

        return response()->json([
            'success' => false,
            'message' => 'Failed to process request',
            'error' => $e->getMessage(),
        ], 500);
    }
}
