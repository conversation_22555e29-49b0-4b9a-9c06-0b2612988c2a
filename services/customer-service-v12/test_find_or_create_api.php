<?php

/**
 * Test script for the new find-or-create-keycloak API
 * This script demonstrates how to use the merged API that reduces API calls
 */

// Test data
$testData = [
    'username' => '************', // Phone number for lookup
    'auth_id' => 'keycloak-uuid-12345', // Keycloak user ID
    'customer_name' => 'Test Customer',
    'email_address' => '<EMAIL>',
    'phone' => '************',
    'company_id' => 8163,
    'unit_id' => 8163,
    'customer_address' => 'Test Address',
    'food_preference' => 'veg',
    'registered_from' => 'api_test',
    'source' => 'test_script'
];

// API endpoint
$apiUrl = 'http://*************:8002/api/v2/customers/find-or-create-keycloak';

// Make the API call
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($testData));
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "Response: $response\n";

// Parse response
$responseData = json_decode($response, true);

if ($responseData && $responseData['success']) {
    echo "\n✅ API Call Successful!\n";
    echo "Action: " . $responseData['action'] . "\n";
    echo "Customer ID: " . $responseData['data']['pk_customer_code'] . "\n";
    echo "Customer Name: " . $responseData['data']['customer_name'] . "\n";
    echo "Phone: " . $responseData['data']['phone'] . "\n";
    
    if ($responseData['action'] === 'found') {
        echo "📋 Customer was found in database\n";
    } else {
        echo "🆕 New customer was created\n";
    }
} else {
    echo "\n❌ API Call Failed!\n";
    if (isset($responseData['message'])) {
        echo "Error: " . $responseData['message'] . "\n";
    }
    if (isset($responseData['errors'])) {
        echo "Validation Errors: " . json_encode($responseData['errors'], JSON_PRETTY_PRINT) . "\n";
    }
}

echo "\n" . str_repeat("=", 50) . "\n";
echo "CURL Command for testing:\n";
echo "curl --location '$apiUrl' \\\n";
echo "--header 'accept: application/json' \\\n";
echo "--header 'Content-Type: application/json' \\\n";
echo "--data-raw '" . json_encode($testData, JSON_PRETTY_PRINT) . "'\n";
