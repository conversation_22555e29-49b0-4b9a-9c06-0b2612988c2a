<?php

namespace App\Services\Auth;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class KeycloakUserRegistrationService
{
    protected array $config;

    public function __construct(array $config)
    {
        $this->config = $config;
    }

    /**
     * Register a new user in Keycloak
     */
    public function registerUser(array $userData, $password): array
    {
        // array:34 [ // app/Services/Auth/KeycloakUserRegistrationService.php:23
        // "profile_visiblity" => "private"
        // "first_name" => "Dinesh"
        // "last_name" => "Koli"
        // "email" => "<EMAIL>"
        // "mobile" => "************"
        // "salutation" => null
        // "gender" => null
        // "dob" => null
        // "wedding_anniversary" => null
        // "pan" => null
        // "aadhaar" => null
        // "blood_group" => null
        // "highest_education" => null
        // "address_line_1" => null
        // "address_line_2" => null
        // "zip_code" => null
        // "city" => null
        // "state" => null
        // "country" => null
        // "avatar" => null
        // "source" => null
        // "vendor_ref_id" => null
        // "created_by" => null
        // "updated_by" => null
        // "t_and_c" => "no"
        // "username" => "************"
        // "status" => "inactive"
        // "updated_at" => "2025-08-01 13:47:40"
        // "created_at" => "2025-08-01 13:47:40"
        // "user_id" => 97215
        // "avatar_large" => null
        // "avatar_medium" => null
        // "avatar_small" => null
        // "password_status" => 1
        // ]
        try {
            // Get admin access token
            $adminToken = $this->getAdminToken();
            
            if (!$adminToken) {
                Log::error('Failed to get Keycloak admin token for registration');
                return [
                    'success' => false,
                    'message' => 'Failed to get Keycloak admin token'
                ];
            }

            $preparedUserData = $this->prepareUserData($userData, $password);

            // Create user in Keycloak
            $response = Http::withToken($adminToken)
                ->post($this->getUsersUrl(), $preparedUserData);

            if ($response->successful() || $response->status() === 201) {
                Log::info('User successfully created in Keycloak', [
                    'username' => $userData['username'],
                    'email' => $userData['email']
                ]);

                // Get the user ID from the Location header or by searching for the user
                $userId = null;
                $locationHeader = $response->header('Location');

                if ($locationHeader) {
                    // Extract user ID from Location header (e.g., .../users/12345-67890-abcdef)
                    $userId = basename($locationHeader);
                } else {
                    // Fallback: search for the user by username
                    $userId = $this->getUserIdByUsername($userData['username'], $adminToken);
                }

                return [
                    'success' => true,
                    'message' => 'User created in Keycloak successfully',
                    'user_id' => $userId
                ];
            } else {
                Log::error('Failed to create user in Keycloak', [
                    'username' => $userData['username'],
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to create user in Keycloak: ' . ($response->json()['errorMessage'] ?? 'Unknown error')
                ];
            }

        } catch (\Exception $e) {
            Log::error('Exception during Keycloak user registration', [
                'username' => $userData['username'],
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak registration failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get admin access token for Keycloak admin operations using service account
     */
    public function getAdminToken(): ?string
    {
        try {
            // Use the startwell client with service account enabled
            $response = Http::asForm()->post($this->getAdminTokenUrl(), [
                'grant_type' => 'client_credentials',
                'client_id' => $this->config['client_id'], // Use the startwell client
                'client_secret' => $this->config['client_secret'],
            ]);

            if ($response->successful()) {
                $tokenData = $response->json();
                Log::info('Successfully obtained Keycloak admin token via service account', [
                    'client_id' => $this->config['client_id']
                ]);
                return $tokenData['access_token'];
            } else {
                Log::error('Failed to get Keycloak admin token via service account', [
                    'client_id' => $this->config['client_id'],
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);
                return null;
            }
        } catch (\Exception $e) {
            Log::error('Exception getting Keycloak admin token via service account', [
                'client_id' => $this->config['client_id'],
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Get admin token URL
     */
    protected function getAdminTokenUrl(): string
    {
        return $this->config['auth_server_url'] . '/realms/' . $this->config['realm'] . '/protocol/openid-connect/token';
    }

    /**
     * Get users management URL
     */
    protected function getUsersUrl(): string
    {
        return $this->config['auth_server_url'] . '/admin/realms/' . $this->config['realm'] . '/users';
    }

    /**
     * Delete user from Keycloak (for rollback purposes)
     */
    public function deleteUser(string $username): bool
    {
        try {
            $adminToken = $this->getAdminToken();
            
            if (!$adminToken) {
                return false;
            }

            // First, find the user by username
            $response = Http::withToken($adminToken)
                ->get($this->getUsersUrl(), ['username' => $username]);

            if (!$response->successful() || empty($response->json())) {
                return false;
            }

            $users = $response->json();
            if (empty($users)) {
                return false;
            }

            $userId = $users[0]['id'];

            // Delete the user
            $deleteResponse = Http::withToken($adminToken)
                ->delete($this->getUsersUrl() . '/' . $userId);

            return $deleteResponse->successful();

        } catch (\Exception $e) {
            Log::error('Exception during Keycloak user deletion', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Update an existing user in Keycloak
     */
    public function updateUser(string $username, array $userData): array
    {
        try {
            // Get admin access token
            $adminToken = $this->getAdminToken();
            
            if (!$adminToken) {
                return [
                    'success' => false,
                    'message' => 'Failed to get Keycloak admin token'
                ];
            }

            // First, find the user by username
            $response = Http::withToken($adminToken)
                ->get($this->getUsersUrl(), ['username' => $username]);

            if (!$response->successful() || empty($response->json())) {
                Log::error('Failed to find user in Keycloak', [
                    'username' => $username,
                    'status' => $response->status(),
                    'response' => $response->json() ?? 'Empty response'
                ]);
                
                return [
                    'success' => false,
                    'message' => 'User not found in Keycloak'
                ];
            }

            $users = $response->json();
            if (empty($users)) {
                return [
                    'success' => false,
                    'message' => 'User not found in Keycloak'
                ];
            }

            $userId = $users[0]['id'];

            // Prepare user data for update
            $keycloakUserUpdate = [
                'firstName' => $userData['first_name'] ?? null,
                'lastName' => $userData['last_name'] ?? null,
                'email' => $userData['email'] ?? null,
            ];

            // Remove null values
            $keycloakUserUpdate = array_filter($keycloakUserUpdate, function ($value) {
                return $value !== null;
            });

            // Update user in Keycloak
            $updateResponse = Http::withToken($adminToken)
                ->put($this->getUsersUrl() . '/' . $userId, $keycloakUserUpdate);

            if ($updateResponse->successful()) {
                Log::info('User successfully updated in Keycloak', [
                    'username' => $username,
                    'userId' => $userId,
                    'email' => $userData['email'] ?? 'not provided'
                ]);

                return [
                    'success' => true,
                    'message' => 'User updated in Keycloak successfully'
                ];
            } else {
                Log::error('Failed to update user in Keycloak', [
                    'username' => $username,
                    'userId' => $userId,
                    'status' => $updateResponse->status(),
                    'response' => $updateResponse->json() ?? 'Empty response'
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to update user in Keycloak: ' . ($updateResponse->json()['errorMessage'] ?? 'Unknown error')
                ];
            }

        } catch (\Exception $e) {
            Log::error('Exception during Keycloak user update', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Keycloak update failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check if user exists in Keycloak
     */
    public function userExists(string $username): bool
    {
        try {
            $adminToken = $this->getAdminToken();

            if (!$adminToken) {
                Log::warning('Failed to get Keycloak admin token for user search', [
                    'username' => $username,
                    'config_client_id' => $this->config['client_id'] ?? 'not_set',
                ]);
                return false;
            }

            // Try searching by username first
            $response = Http::withToken($adminToken)
                ->get($this->getUsersUrl() .'?username='. $username);

            if ($response->successful()) {
                $users = $response->json();
                if (!empty($users)) {
                    Log::info('User found in Keycloak by username', [
                        'username' => $username,
                        'keycloak_user_id' => $users[0]['id'] ?? 'unknown',
                    ]);
                    return true;
                }else{
                    // If not found by username, try searching by email (if username is email format)
                    if (filter_var($username, FILTER_VALIDATE_EMAIL)) {
                        $response = Http::withToken($adminToken)
                            ->get($this->getUsersUrl() .'?email='. $username);

                        if ($response->successful()) {
                            $users = $response->json();
                            if (!empty($users)) {
                                Log::info('User found in Keycloak by email', [
                                    'username' => $username,
                                    'keycloak_user_id' => $users[0]['id'] ?? 'unknown',
                                ]);
                                return true;
                            }else{
                                return false;
                            }
                        }
                    }
                }
            }

            Log::info('User not found in Keycloak', [
                'username' => $username,
            ]);
            return false;

        } catch (\Exception $e) {
            Log::error('Error checking user existence in Keycloak', [
                'username' => $username,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get user ID by username
     */
    protected function getUserIdByUsername(string $username, string $adminToken): ?string
    {
        try {
            $response = Http::withToken($adminToken)
                ->get($this->getUsersUrl(), ['username' => $username]);

            if ($response->successful()) {
                $users = $response->json();
                if (!empty($users) && isset($users[0]['id'])) {
                    return $users[0]['id'];
                }
            }

            Log::warning('Could not find user ID for username', [
                'username' => $username,
                'status' => $response->status()
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception getting user ID by username', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * Update Keycloak user password
     */
    public function updateUserPassword(string $username, string $newPassword): array
    {
        try {
            Log::info('Starting Keycloak password update', [
                'username' => $username,
            ]);

            $adminToken = $this->getAdminToken();

            if (!$adminToken) {
                Log::error('Failed to get Keycloak admin token for password update');
                return [
                    'success' => false,
                    'message' => 'Failed to get Keycloak admin token'
                ];
            }

            // Get user ID
            $userId = $this->getUserIdByUsername($username, $adminToken);

            if (!$userId) {
                Log::error('User not found in Keycloak for password update', [
                    'username' => $username,
                ]);
                return [
                    'success' => false,
                    'message' => 'User not found in Keycloak'
                ];
            }

            // Update password
            $passwordData = [
                'type' => 'password',
                'value' => $newPassword,
                'temporary' => false
            ];

            $response = Http::withToken($adminToken)
                ->put($this->getUsersUrl() . '/' . $userId . '/reset-password', $passwordData);

            if ($response->successful()) {
                Log::info('Keycloak password update successful', [
                    'username' => $username,
                    'user_id' => $userId,
                ]);

                return [
                    'success' => true,
                    'message' => 'Password updated successfully in Keycloak',
                    'user_id' => $userId
                ];
            } else {
                Log::error('Keycloak password update failed', [
                    'username' => $username,
                    'user_id' => $userId,
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to update password in Keycloak: ' . $response->body()
                ];
            }

        } catch (\Exception $e) {
            Log::error('Exception during Keycloak password update', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'Password update failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update Keycloak user details (name, email, etc.)
     */
    public function updateUserDetails(string $username, array $userData): array
    {
        try {
            Log::info('Starting Keycloak user details update', [
                'username' => $username,
                'fields' => array_keys($userData),
            ]);

            $adminToken = $this->getAdminToken();

            if (!$adminToken) {
                Log::error('Failed to get Keycloak admin token for user update');
                return [
                    'success' => false,
                    'message' => 'Failed to get Keycloak admin token'
                ];
            }

            // Get user ID
            $userId = $this->getUserIdByUsername($username, $adminToken);

            if (!$userId) {
                Log::error('User not found in Keycloak for details update', [
                    'username' => $username,
                ]);
                return [
                    'success' => false,
                    'message' => 'User not found in Keycloak'
                ];
            }

            // Prepare update data
            $updateData = [];
            if (isset($userData['first_name'])) {
                $updateData['firstName'] = $userData['first_name'];
            }
            if (isset($userData['last_name'])) {
                $updateData['lastName'] = $userData['last_name'];
            }
            if (isset($userData['email'])) {
                $updateData['email'] = $userData['email'];
            }

            $response = Http::withToken($adminToken)
                ->put($this->getUsersUrl() . '/' . $userId, $updateData);

            if ($response->successful()) {
                Log::info('Keycloak user details update successful', [
                    'username' => $username,
                    'user_id' => $userId,
                    'updated_fields' => array_keys($updateData),
                ]);

                return [
                    'success' => true,
                    'message' => 'User details updated successfully in Keycloak',
                    'user_id' => $userId
                ];
            } else {
                Log::error('Keycloak user details update failed', [
                    'username' => $username,
                    'user_id' => $userId,
                    'status' => $response->status(),
                    'response' => $response->json()
                ]);

                return [
                    'success' => false,
                    'message' => 'Failed to update user details in Keycloak: ' . $response->body()
                ];
            }

        } catch (\Exception $e) {
            Log::error('Exception during Keycloak user details update', [
                'username' => $username,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'User details update failed: ' . $e->getMessage()
            ];
        }
    }

    public function prepareUserData($userData, $password){
        $mobile = $userData['mobile'];
            if ($mobile) {
                $mobile = ltrim($mobile, '+');
                
                // Validate length
                $mobileLength = strlen($mobile);
                if ($mobileLength < 10 || $mobileLength > 12) {
                    Log::warning("Invalid mobile number length for user ID: {$this->user->user_id}. Mobile: {$mobile}");
                    $mobile = null;
                }
            }
            $mobileVerified = 'no';
            if(isset($userData['mobile_verified'])){
                $mobileVerified = $userData['mobile_verified'] ? 'yes' : 'no';
            }
            $emailVerified = false;
            if(isset($userData['email_verified'])){
                $emailVerified = $userData['email_verified'];
            }
            $userData = [
                'username' => $userData['username'],
                'email' => str_replace(' ', '', $userData['email']),
                'firstName' => ($userData['first_name'] == '') ? '-' : $userData['first_name'],
                'lastName' => ($userData['last_name'] == '') ? '-' : $userData['last_name'],
                'enabled' => true,
                "emailVerified" => true, // Always set to true to avoid "Account is not fully set up" error
                "attributes" => [
                    "mobile" => [$mobile],
                    "mobileVerified" => [$mobileVerified],
                    "oldSsoUserId" => [$userData['user_id']]
                ],
                'credentials' => [
                    [
                        'type' => 'password',
                        'value' => $password,
                        'temporary' => false
                    ]
                ],
                'requiredActions' => [], // Ensure no required actions are set to avoid setup issues
            ];
            return $userData;
        }
}